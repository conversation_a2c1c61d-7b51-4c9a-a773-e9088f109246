name: snapback-site

services:
  postgres:
    image: postgres
    container_name: snapback-site-postgres
    environment:
      POSTGRES_DB: snapback-site
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - snapback-site_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  snapback-site_postgres_data: