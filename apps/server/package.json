{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:start": "docker compose up -d", "db:watch": "docker compose up", "db:stop": "docker compose stop", "db:down": "docker compose down"}, "dependencies": {"dotenv": "^17.2.1", "zod": "^4.0.2", "express": "^5.1.0", "cors": "^2.8.5", "@prisma/client": "^6.13.0", "better-auth": "^1.3.4"}, "devDependencies": {"tsdown": "^0.12.9", "typescript": "^5.8.2", "@types/express": "^5.0.1", "@types/cors": "^2.8.17", "tsx": "^4.19.2", "@types/node": "^22.13.11", "prisma": "^6.13.0"}}